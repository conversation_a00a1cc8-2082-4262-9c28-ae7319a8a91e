"""
多进程文本向量编码器
将文本→向量的过程改为多进程并行处理，提升CPU利用率
"""
import os
import time
import multiprocessing as mp
from multiprocessing import Pool, Queue, Manager
from typing import List, Dict, Any, Tuple, Optional
from loguru import logger
import numpy as np
import hashlib
import pickle
import sys
from pathlib import Path

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from idconfig.config import Config


def init_worker(model_path: str, device: str, config_dict: dict):
    """
    工作进程初始化函数
    每个工作进程都会调用这个函数来初始化自己的模型实例
    """
    global worker_model, worker_config
    
    try:
        # 设置环境变量
        os.environ['OMP_NUM_THREADS'] = str(config_dict.get('OMP_NUM_THREADS', 2))
        os.environ['OPENBLAS_NUM_THREADS'] = str(config_dict.get('OMP_NUM_THREADS', 2))
        
        # 导入必要的库
        from sentence_transformers import SentenceTransformer
        import torch
        
        # 设置PyTorch线程数（每个进程使用较少线程）
        torch.set_num_threads(2)
        torch.set_grad_enabled(False)
        
        # 初始化模型
        worker_model = SentenceTransformer(
            model_path,
            device=device,
            trust_remote_code=True
        )
        
        # 设置为评估模式
        worker_model.eval()
        
        # 禁用梯度计算
        for param in worker_model.parameters():
            param.requires_grad = False
        
        # 保存配置
        worker_config = config_dict
        
        logger.info(f"工作进程 {os.getpid()} 初始化完成")
        
    except Exception as e:
        logger.error(f"工作进程初始化失败: {e}")
        raise


def preprocess_text(text: str, config: dict) -> str:
    """预处理文本"""
    if not config.get('ENABLE_TEXT_PREPROCESSING', True):
        return text
    
    # 移除多余空白字符
    text = ' '.join(text.split())
    
    # 限制文本长度
    max_length = config.get('MAX_TEXT_LENGTH', 512)
    if len(text) > max_length:
        if config.get('ENABLE_SMART_TRUNCATION', True):
            # 智能截断
            half_length = max_length // 2 - 50
            text = text[:half_length] + "..." + text[-half_length:]
        else:
            # 简单截断
            text = text[:max_length]
    
    return text


def encode_text_chunk(texts_with_indices: List[Tuple[int, str]]) -> List[Tuple[int, List[float]]]:
    """
    编码文本块的工作函数
    每个进程会调用这个函数来处理分配给它的文本块
    """
    global worker_model, worker_config
    
    results = []
    
    try:
        import torch
        
        # 预处理文本
        processed_texts = []
        indices = []
        
        for idx, text in texts_with_indices:
            processed_text = preprocess_text(text, worker_config)
            processed_texts.append(processed_text)
            indices.append(idx)
        
        # 批量编码
        with torch.no_grad():
            embeddings = worker_model.encode(
                processed_texts,
                normalize_embeddings=True,
                batch_size=len(processed_texts),
                show_progress_bar=False,
                convert_to_numpy=True
            )
        
        # 组装结果
        for idx, embedding in zip(indices, embeddings):
            results.append((idx, embedding.tolist()))
        
        logger.debug(f"进程 {os.getpid()} 处理了 {len(texts_with_indices)} 个文本")
        
    except Exception as e:
        logger.error(f"进程 {os.getpid()} 编码失败: {e}")
        # 返回空向量作为失败的占位符
        for idx, _ in texts_with_indices:
            results.append((idx, []))
    
    return results


class MultiprocessEncoder:
    """多进程文本编码器"""
    
    def __init__(self, config: Config):
        self.config = config
        self.model_path = config.EMBEDDING_MODEL
        self.device = 'cpu'  # 多进程模式下使用CPU
        self.max_workers = config.MAX_WORKERS
        self.chunk_size = config.MULTIPROCESS_CHUNK_SIZE
        self.enable_multiprocessing = config.ENABLE_MULTIPROCESSING
        
        # 性能统计
        self.stats = {
            "total_texts": 0,
            "total_time": 0.0,
            "process_count": 0
        }
        
        logger.info(f"多进程编码器初始化 - 工作进程数: {self.max_workers}, 块大小: {self.chunk_size}")
    
    def encode_texts(self, texts: List[str]) -> List[List[float]]:
        """
        多进程编码文本列表
        """
        if not texts:
            return []
        
        if not self.enable_multiprocessing or len(texts) < self.chunk_size:
            # 文本数量较少时，使用单进程处理
            return self._encode_single_process(texts)
        
        return self._encode_multiprocess(texts)
    
    def _encode_single_process(self, texts: List[str]) -> List[List[float]]:
        """单进程编码（回退方案）"""
        logger.info(f"使用单进程编码 {len(texts)} 个文本")
        
        try:
            # 临时初始化模型
            from sentence_transformers import SentenceTransformer
            import torch
            
            torch.set_num_threads(self.config.PYTORCH_NUM_THREADS)
            torch.set_grad_enabled(False)
            
            model = SentenceTransformer(
                self.model_path,
                device=self.device,
                trust_remote_code=True
            )
            model.eval()
            
            # 预处理文本
            processed_texts = [
                preprocess_text(text, self.config.__dict__) 
                for text in texts
            ]
            
            # 批量编码
            with torch.no_grad():
                embeddings = model.encode(
                    processed_texts,
                    normalize_embeddings=True,
                    batch_size=min(16, len(processed_texts)),
                    show_progress_bar=False,
                    convert_to_numpy=True
                )
            
            return [emb.tolist() for emb in embeddings]
            
        except Exception as e:
            logger.error(f"单进程编码失败: {e}")
            return [[0.0] * self.config.VECTOR_DIM for _ in texts]
    
    def _encode_multiprocess(self, texts: List[str]) -> List[List[float]]:
        """多进程编码文本"""
        start_time = time.time()
        
        logger.info(f"开始多进程编码 {len(texts)} 个文本，使用 {self.max_workers} 个进程")
        
        try:
            # 准备配置字典（用于传递给工作进程）
            config_dict = {
                'OMP_NUM_THREADS': self.config.OMP_NUM_THREADS,
                'ENABLE_TEXT_PREPROCESSING': self.config.ENABLE_TEXT_PREPROCESSING,
                'MAX_TEXT_LENGTH': self.config.MAX_TEXT_LENGTH,
                'ENABLE_SMART_TRUNCATION': self.config.ENABLE_SMART_TRUNCATION,
                'VECTOR_DIM': self.config.VECTOR_DIM
            }
            
            # 将文本分块，每个块包含索引和文本
            chunks = []
            for i in range(0, len(texts), self.chunk_size):
                chunk = []
                for j in range(i, min(i + self.chunk_size, len(texts))):
                    chunk.append((j, texts[j]))
                chunks.append(chunk)
            
            logger.info(f"文本分为 {len(chunks)} 个块，每块最多 {self.chunk_size} 个文本")
            
            # 创建进程池并处理
            with Pool(
                processes=self.max_workers,
                initializer=init_worker,
                initargs=(self.model_path, self.device, config_dict)
            ) as pool:
                
                # 提交所有任务
                results = pool.map(encode_text_chunk, chunks)
            
            # 合并结果
            all_embeddings = [None] * len(texts)
            
            for chunk_results in results:
                for idx, embedding in chunk_results:
                    if idx < len(all_embeddings):
                        all_embeddings[idx] = embedding
            
            # 处理失败的编码（用零向量填充）
            for i, embedding in enumerate(all_embeddings):
                if embedding is None or len(embedding) == 0:
                    all_embeddings[i] = [0.0] * self.config.VECTOR_DIM
                    logger.warning(f"文本 {i} 编码失败，使用零向量")
            
            # 更新统计信息
            total_time = time.time() - start_time
            self.stats["total_texts"] += len(texts)
            self.stats["total_time"] += total_time
            self.stats["process_count"] += 1
            
            throughput = len(texts) / total_time if total_time > 0 else 0
            logger.info(f"多进程编码完成，耗时: {total_time:.2f}秒，吞吐量: {throughput:.2f}文本/秒")
            
            return all_embeddings
            
        except Exception as e:
            logger.error(f"多进程编码失败: {e}")
            # 回退到单进程
            return self._encode_single_process(texts)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        avg_time = 0.0
        avg_throughput = 0.0
        
        if self.stats["process_count"] > 0:
            avg_time = self.stats["total_time"] / self.stats["process_count"]
        
        if self.stats["total_time"] > 0:
            avg_throughput = self.stats["total_texts"] / self.stats["total_time"]
        
        return {
            "total_texts_processed": self.stats["total_texts"],
            "total_processing_time": f"{self.stats['total_time']:.2f}s",
            "batch_count": self.stats["process_count"],
            "avg_batch_time": f"{avg_time:.2f}s",
            "avg_throughput": f"{avg_throughput:.2f} texts/sec",
            "max_workers": self.max_workers,
            "chunk_size": self.chunk_size,
            "multiprocessing_enabled": self.enable_multiprocessing
        }
    
    def clear_stats(self):
        """清空统计信息"""
        self.stats = {
            "total_texts": 0,
            "total_time": 0.0,
            "process_count": 0
        }
        logger.info("多进程编码器统计信息已清空")
