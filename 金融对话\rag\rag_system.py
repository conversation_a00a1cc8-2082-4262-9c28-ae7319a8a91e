"""
RAG检索系统
负责文档嵌入、相似度搜索和知识检索功能
"""
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Any
from loguru import logger
import numpy as np
import sys
from pathlib import Path
import gc
import psutil
import os

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from rag.milvus_manager import MilvusManager
from idconfig.config import Config

class RAGSystem:
    def __init__(self):
        self.config = Config()
        self.embedding_model = None
        self.milvus_manager = MilvusManager()
        self._text_cache = {}  # 添加文本缓存
        self._max_cache_size = self.config.MAX_CACHE_SIZE  # 使用配置文件中的缓存大小

        # 性能统计
        self._performance_stats = {
            "total_encodings": 0,
            "cache_hits": 0,
            "total_time": 0.0,
            "batch_count": 0
        }

    def _setup_cpu_optimization(self):
        """设置CPU优化环境变量"""
        try:
            # 设置OpenMP线程数
            os.environ['OMP_NUM_THREADS'] = str(self.config.OMP_NUM_THREADS)
            # 设置BLAS线程数
            os.environ['OPENBLAS_NUM_THREADS'] = str(self.config.OMP_NUM_THREADS)
            # 禁用TensorFlow的GPU使用（如果存在）
            os.environ['CUDA_VISIBLE_DEVICES'] = ''

            logger.info(f"CPU优化环境变量设置完成: OMP={self.config.OMP_NUM_THREADS}")
        except Exception as e:
            logger.warning(f"设置CPU优化环境变量失败: {e}")

    def _get_optimal_batch_size(self, total_texts: int) -> int:
        """根据系统资源和文本数量计算最优批处理大小"""
        if not self.config.ADAPTIVE_BATCH_SIZE:
            return self.config.DEFAULT_BATCH_SIZE

        try:
            # 获取当前内存使用情况
            memory_percent = psutil.virtual_memory().percent

            # 根据内存使用情况和文本数量调整批处理大小
            if memory_percent > self.config.MEMORY_THRESHOLD:
                # 内存紧张，使用较小的批处理
                batch_size = max(4, self.config.DEFAULT_BATCH_SIZE // 2)
            elif memory_percent < 50:
                # 内存充足，可以使用较大的批处理
                batch_size = min(self.config.MAX_BATCH_SIZE,
                               max(self.config.DEFAULT_BATCH_SIZE, total_texts // 8))
            else:
                # 正常情况
                batch_size = self.config.DEFAULT_BATCH_SIZE

            # 确保批处理大小不超过文本总数
            batch_size = min(batch_size, total_texts)

            logger.debug(f"自适应批处理大小: {batch_size} (内存使用: {memory_percent:.1f}%)")
            return batch_size

        except Exception as e:
            logger.warning(f"计算最优批处理大小失败: {e}")
            return self.config.DEFAULT_BATCH_SIZE

    def initialize(self):
        """初始化RAG系统"""
        try:
            # 设置CPU优化环境变量
            self._setup_cpu_optimization()

            # 初始化嵌入模型
            logger.info(f"加载嵌入模型: {self.config.EMBEDDING_MODEL}")

            # 优化BGE-M3模型在CPU上的性能
            import torch
            device = 'cuda' if torch.cuda.is_available() else 'cpu'
            logger.info(f"使用设备: {device}")

            # 设置CPU优化参数
            if device == 'cpu':
                # 使用配置文件中的线程数设置
                num_threads = min(self.config.PYTORCH_NUM_THREADS, os.cpu_count())
                torch.set_num_threads(num_threads)
                logger.info(f"设置PyTorch线程数: {torch.get_num_threads()}")

                # 设置推理优化
                torch.set_grad_enabled(False)  # 全局禁用梯度计算

                # 启用MKL-DNN优化（如果可用）
                if hasattr(torch.backends, 'mkldnn') and torch.backends.mkldnn.is_available():
                    torch.backends.mkldnn.enabled = True
                    logger.info("启用MKL-DNN优化")

            self.embedding_model = SentenceTransformer(
                self.config.EMBEDDING_MODEL,
                device=device,
                trust_remote_code=True
            )

            # 如果是CPU，启用更多优化
            if device == 'cpu':
                # 设置为评估模式以提高推理速度
                self.embedding_model.eval()
                # 禁用梯度计算
                for param in self.embedding_model.parameters():
                    param.requires_grad = False
                logger.info("已启用CPU优化设置")

            logger.info("嵌入模型加载完成")

            # 验证模型配置
            if not self._validate_model_config():
                return False

            # 初始化Milvus管理器
            if not self.milvus_manager.initialize():
                logger.error("Milvus管理器初始化失败")
                return False

            logger.info("RAG系统初始化完成")
            return True

        except Exception as e:
            logger.error(f"RAG系统初始化失败: {e}")
            return False

    def _validate_model_config(self):
        """验证模型配置"""
        try:
            # 测试编码一个简单文本
            test_text = "测试文本"
            test_embedding = self.embedding_model.encode(test_text, normalize_embeddings=True)

            actual_dim = len(test_embedding)
            expected_dim = self.config.VECTOR_DIM

            logger.info(f"模型实际维度: {actual_dim}, 配置维度: {expected_dim}")

            # 检查是否为BGE-M3模型
            if "bge-m3" in self.config.EMBEDDING_MODEL.lower():
                if actual_dim != 1024:
                    logger.error(f"BGE-M3模型应该是1024维，但检测到{actual_dim}维")
                    return False
                if expected_dim != 1024:
                    logger.error("BGE-M3模型需要设置VECTOR_DIM=1024")
                    return False
                logger.info("✓ BGE-M3模型配置验证通过")

            # 检查向量是否归一化
            norm = np.linalg.norm(test_embedding)
            if abs(norm - 1.0) > 0.01:
                logger.warning(f"向量未完全归一化，norm={norm:.4f}")
            else:
                logger.info("✓ 向量归一化验证通过")

            return True

        except Exception as e:
            logger.error(f"模型配置验证失败: {e}")
            return False

    def _preprocess_text(self, text: str) -> str:
        """预处理文本以提高编码效率"""
        if not self.config.ENABLE_TEXT_PREPROCESSING:
            return text

        # 快速检查是否需要处理
        if len(text) <= self.config.MAX_TEXT_LENGTH and not text.strip() != text:
            return text

        # 移除多余的空白字符（使用更高效的方法）
        text = ' '.join(text.split())

        # 限制文本长度以提高处理速度
        max_length = self.config.MAX_TEXT_LENGTH
        if len(text) > max_length:
            if self.config.ENABLE_SMART_TRUNCATION:
                # 智能截断：保留开头和结尾
                half_length = max_length // 2 - 50
                text = text[:half_length] + "..." + text[-half_length:]
            else:
                # 简单截断
                text = text[:max_length]

        return text

    def _cache_embedding(self, text_hash: str, embedding: List[float]):
        """缓存嵌入向量"""
        if not self.config.ENABLE_TEXT_CACHE:
            return

        # 如果缓存已满，删除最旧的条目
        if len(self._text_cache) >= self._max_cache_size:
            # 删除第一个条目（最旧的）
            oldest_key = next(iter(self._text_cache))
            del self._text_cache[oldest_key]

        self._text_cache[text_hash] = embedding

    def clear_cache(self):
        """清空缓存"""
        self._text_cache.clear()
        logger.info("已清空文本缓存")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "cache_size": len(self._text_cache),
            "max_cache_size": self._max_cache_size,
            "cache_usage": f"{len(self._text_cache)}/{self._max_cache_size}"
        }

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        cache_hit_rate = 0.0
        avg_time = 0.0

        if self._performance_stats["total_encodings"] > 0:
            cache_hit_rate = (self._performance_stats["cache_hits"] /
                            self._performance_stats["total_encodings"] * 100)
            avg_time = (self._performance_stats["total_time"] /
                       self._performance_stats["total_encodings"])

        return {
            "total_encodings": self._performance_stats["total_encodings"],
            "cache_hits": self._performance_stats["cache_hits"],
            "cache_hit_rate": f"{cache_hit_rate:.1f}%",
            "avg_time_per_encoding": f"{avg_time:.3f}s",
            "total_time": f"{self._performance_stats['total_time']:.2f}s",
            "batch_count": self._performance_stats["batch_count"]
        }
    
    def encode_text(self, text: str) -> List[float]:
        """将文本编码为向量，支持缓存和批量优化"""
        import time
        import hashlib
        from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError

        # 更新统计
        self._performance_stats["total_encodings"] += 1

        # 检查缓存
        text_hash = hashlib.md5(text.encode('utf-8')).hexdigest()
        if self.config.ENABLE_TEXT_CACHE and text_hash in self._text_cache:
            self._performance_stats["cache_hits"] += 1
            return self._text_cache[text_hash]

        # 文本预处理和优化
        processed_text = self._preprocess_text(text)

        def encode_with_timeout():
            """在单独线程中执行编码，支持超时"""
            import torch
            with torch.no_grad():  # 禁用梯度计算以节省内存和计算
                return self.embedding_model.encode(
                    processed_text,
                    normalize_embeddings=True,
                    batch_size=1,  # 单个文本处理
                    show_progress_bar=False,
                    convert_to_numpy=True
                )

        try:
            if not self.embedding_model:
                logger.error("嵌入模型未初始化")
                return []

            start_time = time.time()

            # 使用线程池实现跨平台超时机制
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(encode_with_timeout)
                try:
                    # 根据文本长度动态调整超时时间
                    timeout = min(60, max(30, len(text) // 50))  # 30-60秒动态超时
                    embedding = future.result(timeout=timeout)
                except FutureTimeoutError:
                    raise TimeoutError("向量编码超时")

            encode_time = time.time() - start_time

            # 更新性能统计
            self._performance_stats["total_time"] += encode_time

            if encode_time > 3:  # 降低警告阈值到3秒
                logger.warning(f"向量编码耗时较长: {encode_time:.2f}秒，文本长度: {len(text)}")

            # 定期输出性能统计
            if self._performance_stats["total_encodings"] % 100 == 0:
                stats = self.get_performance_stats()
                logger.info(f"编码性能统计 - 总数: {stats['total_encodings']}, "
                          f"缓存命中率: {stats['cache_hit_rate']}, "
                          f"平均耗时: {stats['avg_time_per_encoding']}")

            # 检查向量维度
            actual_dim = len(embedding)
            expected_dim = self.config.VECTOR_DIM

            if actual_dim != expected_dim:
                logger.warning(f"向量维度不匹配: {actual_dim} != {expected_dim}")
                # BGE-M3应该是1024维，如果不匹配说明配置有问题
                if actual_dim == 1024 and expected_dim != 1024:
                    logger.error("检测到BGE-M3模型(1024维)，但配置的VECTOR_DIM不是1024")
                    logger.error("请在config.py中设置 VECTOR_DIM = 1024")
                    return []

                # 如果维度不匹配，进行填充或截断（不推荐）
                if actual_dim < expected_dim:
                    embedding = np.pad(embedding, (0, expected_dim - actual_dim))
                    logger.warning(f"向量已填充到{expected_dim}维")
                else:
                    embedding = embedding[:expected_dim]
                    logger.warning(f"向量已截断到{expected_dim}维")

            # 确保向量是归一化的（BGE-M3推荐使用归一化向量）
            if not hasattr(embedding, 'dtype'):
                embedding = np.array(embedding, dtype=np.float32)

            # 检查向量是否有效
            if np.any(np.isnan(embedding)) or np.any(np.isinf(embedding)):
                logger.error("生成的向量包含无效值(NaN或Inf)")
                return []

            result = embedding.tolist()

            # 缓存结果
            self._cache_embedding(text_hash, result)

            return result

        except TimeoutError:
            logger.error(f"向量编码超时，文本长度: {len(text)}")
            return []
        except Exception as e:
            logger.error(f"文本编码失败: {e}")
            return []

    def encode_texts_batch(self, texts: List[str], batch_size: int = None) -> List[List[float]]:
        """批量编码文本，提高效率"""
        import time
        import torch

        if not texts:
            return []

        # 自适应批处理大小
        if batch_size is None:
            batch_size = self._get_optimal_batch_size(len(texts))

        logger.info(f"开始批量编码 {len(texts)} 个文本，批次大小: {batch_size}")

        all_embeddings = []
        total_batches = (len(texts) + batch_size - 1) // batch_size

        # 更新批次统计
        self._performance_stats["batch_count"] += 1

        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_num = i // batch_size + 1

            try:
                start_time = time.time()

                # 预处理批次文本
                processed_texts = [self._preprocess_text(text) for text in batch_texts]

                # 批量编码
                with torch.no_grad():
                    batch_embeddings = self.embedding_model.encode(
                        processed_texts,
                        normalize_embeddings=True,
                        batch_size=len(processed_texts),
                        show_progress_bar=False,
                        convert_to_numpy=True
                    )

                # 转换为列表格式
                for embedding in batch_embeddings:
                    all_embeddings.append(embedding.tolist())

                batch_time = time.time() - start_time
                logger.info(f"批次 {batch_num}/{total_batches} 完成，耗时: {batch_time:.2f}秒，"
                           f"平均每个文本: {batch_time/len(batch_texts):.2f}秒")

                # 智能内存管理
                memory_percent = psutil.virtual_memory().percent
                if (memory_percent > self.config.MEMORY_THRESHOLD or
                    batch_num % self.config.GC_FREQUENCY == 0):
                    import gc
                    gc.collect()
                    logger.debug(f"执行垃圾回收，内存使用: {memory_percent:.1f}%")

            except Exception as e:
                logger.error(f"批次 {batch_num} 编码失败: {e}")
                # 回退到单个编码
                for text in batch_texts:
                    embedding = self.encode_text(text)
                    all_embeddings.append(embedding if embedding else [0.0] * self.config.VECTOR_DIM)

        logger.info(f"批量编码完成，共处理 {len(all_embeddings)} 个文本")
        return all_embeddings

    def add_knowledge(self, content: str, category: str, source: str = ""):
        """添加知识到知识库"""
        try:
            # 生成嵌入向量
            embedding = self.encode_text(content)
            if not embedding:
                logger.error("生成嵌入向量失败")
                return False
            
            # 准备数据
            data = [{
                "content": content,
                "category": category,
                "source": source,
                "embedding": embedding
            }]
            
            # 插入到Milvus
            return self.milvus_manager.insert_knowledge(data)
            
        except Exception as e:
            logger.error(f"添加知识失败: {e}")
            return False
    
    def add_conversation_history(self, session_id: str, user_query: str, 
                               assistant_response: str, timestamp: int):
        """添加对话历史"""
        try:
            # 将用户查询和助手回复组合作为嵌入内容
            combined_text = f"用户: {user_query}\n助手: {assistant_response}"
            
            # 生成嵌入向量
            embedding = self.encode_text(combined_text)
            if not embedding:
                logger.error("生成嵌入向量失败")
                return False
            
            # 准备数据
            data = [{
                "session_id": session_id,
                "user_query": user_query,
                "assistant_response": assistant_response,
                "timestamp": timestamp,
                "embedding": embedding
            }]
            
            # 插入到Milvus
            return self.milvus_manager.insert_history(data)
            
        except Exception as e:
            logger.error(f"添加对话历史失败: {e}")
            return False
    
    def search_knowledge(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """搜索相关知识"""
        try:
            # 生成查询向量
            query_embedding = self.encode_text(query)
            if not query_embedding:
                logger.error("生成查询向量失败")
                return []
            
            # 搜索知识库
            results = self.milvus_manager.search_knowledge(query_embedding, top_k)
            
            # 过滤低相似度结果
            filtered_results = [
                result for result in results 
                if result["score"] >= self.config.SIMILARITY_THRESHOLD
            ]
            
            logger.info(f"知识库搜索完成，找到 {len(filtered_results)} 条相关结果")
            return filtered_results
            
        except Exception as e:
            logger.error(f"搜索知识失败: {e}")
            return []
    
    def search_conversation_history(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """搜索相关对话历史"""
        try:
            # 生成查询向量
            query_embedding = self.encode_text(query)
            if not query_embedding:
                logger.error("生成查询向量失败")
                return []
            
            # 搜索历史对话
            results = self.milvus_manager.search_history(query_embedding, top_k)
            
            # 过滤低相似度结果
            filtered_results = [
                result for result in results 
                if result["score"] >= self.config.SIMILARITY_THRESHOLD
            ]
            
            logger.info(f"历史对话搜索完成，找到 {len(filtered_results)} 条相关结果")
            return filtered_results
            
        except Exception as e:
            logger.error(f"搜索对话历史失败: {e}")
            return []
    
    def retrieve_context(self, query: str, include_images: bool = True) -> Dict[str, Any]:
        """检索相关上下文信息"""
        try:
            # 搜索知识库
            knowledge_results = self.search_knowledge(query)

            # 搜索历史对话
            history_results = self.search_conversation_history(query)

            # 搜索相关图片（如果启用）
            image_results = []
            if include_images:
                image_results = self.search_related_images(query)

            # 组织上下文信息
            context = {
                "knowledge": knowledge_results,
                "history": history_results,
                "images": image_results,
                "query": query
            }

            logger.info(f"上下文检索完成 - 知识: {len(knowledge_results)}条, 历史: {len(history_results)}条, 图片: {len(image_results)}张")
            return context

        except Exception as e:
            logger.error(f"检索上下文失败: {e}")
            return {"knowledge": [], "history": [], "images": [], "query": query}
    
    def batch_add_knowledge(self, knowledge_list: List[Dict[str, str]]):
        """批量添加知识"""
        try:
            logger.info(f"开始批量添加 {len(knowledge_list)} 个知识块到向量数据库")
            data = []
            total_count = len(knowledge_list)

            # 使用自适应批处理大小
            encoding_batch_size = self._get_optimal_batch_size(total_count)

            # 分批处理，避免内存问题
            for i in range(0, total_count, encoding_batch_size):
                # 收集批次文本进行批量编码
                batch_items = knowledge_list[i:i + encoding_batch_size]
                batch_texts = [item["content"] for item in batch_items]

                try:
                    # 记录进度
                    logger.info(f"正在生成向量: {i + 1}-{min(i + len(batch_texts), total_count)}/{total_count} "
                               f"({((i + len(batch_texts)) / total_count * 100):.1f}%)")

                    # 批量编码
                    batch_embeddings = self.encode_texts_batch(batch_texts)

                    # 处理编码结果
                    for item, embedding in zip(batch_items, batch_embeddings):
                        if embedding and len(embedding) > 0:
                            data.append({
                                "content": item["content"],
                                "category": item.get("category", "general"),
                                "source": item.get("source", ""),
                                "embedding": embedding
                            })
                        else:
                            logger.warning(f"跳过无法生成向量的知识块: {item.get('content', '')[:100]}...")

                    # 检查内存使用情况
                    memory_percent = psutil.virtual_memory().percent
                    if memory_percent > 80:
                        logger.warning(f"内存使用率较高: {memory_percent:.1f}%，执行垃圾回收")
                        gc.collect()

                    # 插入到数据库
                    if data:
                        logger.info(f"插入批次数据到数据库: {len(data)} 个知识块 (内存使用: {memory_percent:.1f}%)")
                        if not self.milvus_manager.insert_knowledge(data):
                            logger.error(f"批次插入失败，已处理 {i + len(batch_texts)} 个知识块")
                            return False
                        data = []  # 清空已处理的数据
                        gc.collect()  # 强制垃圾回收

                    # 更新索引
                    i += len(batch_texts)

                except Exception as e:
                    logger.error(f"处理批次 {i + 1}-{min(i + encoding_batch_size, total_count)} 时出错: {e}")
                    # 回退到单个处理
                    for item in batch_items:
                        try:
                            embedding = self.encode_text(item["content"])
                            if embedding:
                                data.append({
                                    "content": item["content"],
                                    "category": item.get("category", "general"),
                                    "source": item.get("source", ""),
                                    "embedding": embedding
                                })
                        except Exception as single_error:
                            logger.error(f"单个处理也失败: {single_error}")

            # 插入剩余的数据
            if data:
                logger.info(f"插入最后批次数据到数据库: {len(data)} 个知识块")
                if not self.milvus_manager.insert_knowledge(data):
                    logger.error("最后批次插入失败")
                    return False

            logger.info(f"成功完成批量添加，共处理 {total_count} 个知识块")
            return True

        except Exception as e:
            logger.error(f"批量添加知识失败: {e}")
            return False

    def search_related_images(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """搜索与查询相关的图片"""
        try:
            # 导入多模态检索器
            from rag.multimodal_retrieval import MultimodalImageRetriever

            # 创建多模态检索器实例
            multimodal_retriever = MultimodalImageRetriever()
            if not multimodal_retriever.initialize():
                logger.warning("多模态检索器初始化失败，跳过图片搜索")
                return []

            # 搜索相关图片
            image_results = multimodal_retriever.search_images_by_text(query, top_k)

            # 过滤和格式化结果
            formatted_results = []
            for result in image_results:
                # 使用final_score作为主要评分标准
                score = result.get("final_score", result.get("similarity", 0))
                if score >= 0.2:  # 设置相似度阈值
                    formatted_result = {
                        "image_id": result.get("image_id"),
                        "pdf_name": result.get("pdf_name"),
                        "page_number": result.get("page_number"),
                        "image_index": result.get("image_index"),
                        "description": result.get("description"),
                        "image_type": result.get("image_type"),
                        "similarity": result.get("similarity", 0),
                        "final_score": score,
                        "width": result.get("width"),
                        "height": result.get("height")
                    }
                    formatted_results.append(formatted_result)

            logger.info(f"图片搜索完成，找到 {len(formatted_results)} 张相关图片")
            return formatted_results

        except ImportError:
            logger.warning("多模态功能不可用，跳过图片搜索")
            return []
        except Exception as e:
            logger.error(f"搜索相关图片失败: {e}")
            return []
