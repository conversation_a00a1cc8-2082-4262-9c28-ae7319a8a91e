#!/usr/bin/env python3
"""
CPU性能优化测试脚本
测试向量编码、文本预处理和批处理效率的优化效果
"""
import sys
import time
import psutil
from pathlib import Path
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from rag.rag_system import RAGSystem
from idconfig.config import Config


def test_single_encoding_performance():
    """测试单个文本编码性能"""
    logger.info("=== 测试单个文本编码性能 ===")
    
    try:
        rag_system = RAGSystem()
        if not rag_system.initialize():
            logger.error("RAG系统初始化失败")
            return False
        
        # 测试文本
        test_texts = [
            "金融市场分析报告显示，当前投资环境存在多重机遇与挑战。",
            "银行业数字化转型正在加速推进，人工智能技术应用日益广泛。",
            "区块链技术在金融领域的应用前景广阔，但监管政策仍需完善。",
            "ESG投资理念正在成为全球资本市场的重要发展趋势。",
            "量化交易策略在现代投资管理中占据越来越重要的地位。"
        ]
        
        # 测试单个编码性能
        total_time = 0
        successful_encodings = 0
        
        logger.info("开始单个文本编码测试...")
        start_time = time.time()
        
        for i, text in enumerate(test_texts):
            text_start = time.time()
            embedding = rag_system.encode_text(text)
            text_time = time.time() - text_start
            
            if embedding:
                successful_encodings += 1
                logger.info(f"文本 {i+1}: 编码成功，耗时 {text_time:.3f}秒，维度 {len(embedding)}")
            else:
                logger.error(f"文本 {i+1}: 编码失败")
            
            total_time += text_time
        
        # 测试缓存效果
        logger.info("测试缓存效果...")
        cache_start = time.time()
        for text in test_texts[:3]:  # 重复编码前3个文本
            rag_system.encode_text(text)
        cache_time = time.time() - cache_start
        
        # 输出结果
        avg_time = total_time / len(test_texts) if test_texts else 0
        logger.info(f"单个编码测试完成:")
        logger.info(f"  成功率: {successful_encodings}/{len(test_texts)} ({successful_encodings/len(test_texts)*100:.1f}%)")
        logger.info(f"  平均耗时: {avg_time:.3f}秒")
        logger.info(f"  缓存测试耗时: {cache_time:.3f}秒")
        
        # 显示性能统计
        stats = rag_system.get_performance_stats()
        logger.info("性能统计:")
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        logger.error(f"单个编码性能测试失败: {e}")
        return False


def test_batch_encoding_performance():
    """测试批量编码性能"""
    logger.info("=== 测试批量编码性能 ===")
    
    try:
        rag_system = RAGSystem()
        if not rag_system.initialize():
            logger.error("RAG系统初始化失败")
            return False
        
        # 生成测试文本
        base_texts = [
            "金融科技创新推动行业数字化转型发展。",
            "人工智能在风险管理领域的应用日益成熟。",
            "区块链技术为金融服务带来新的可能性。",
            "绿色金融政策支持可持续发展目标实现。",
            "量化投资策略优化资产配置效率。"
        ]
        
        # 扩展到不同数量进行测试
        test_cases = [
            (10, base_texts * 2),
            (25, base_texts * 5),
            (50, base_texts * 10)
        ]
        
        results = []
        
        for count, texts in test_cases:
            logger.info(f"测试批量编码 {count} 个文本...")
            
            # 记录系统资源
            cpu_before = psutil.cpu_percent()
            memory_before = psutil.virtual_memory().percent
            
            start_time = time.time()
            embeddings = rag_system.encode_texts_batch(texts)
            batch_time = time.time() - start_time
            
            cpu_after = psutil.cpu_percent()
            memory_after = psutil.virtual_memory().percent
            
            success_count = len([e for e in embeddings if e])
            throughput = success_count / batch_time if batch_time > 0 else 0
            
            result = {
                "count": count,
                "success_rate": success_count / len(texts) * 100,
                "total_time": batch_time,
                "avg_time": batch_time / len(texts),
                "throughput": throughput,
                "cpu_usage": cpu_after - cpu_before,
                "memory_usage": memory_after - memory_before
            }
            
            results.append(result)
            
            logger.info(f"  成功率: {result['success_rate']:.1f}%")
            logger.info(f"  总耗时: {result['total_time']:.2f}秒")
            logger.info(f"  平均耗时: {result['avg_time']:.3f}秒/文本")
            logger.info(f"  吞吐量: {result['throughput']:.2f}文本/秒")
            logger.info(f"  CPU变化: {result['cpu_usage']:+.1f}%")
            logger.info(f"  内存变化: {result['memory_usage']:+.1f}%")
            logger.info("")
        
        # 分析性能趋势
        logger.info("批量编码性能分析:")
        for i, result in enumerate(results):
            if i > 0:
                prev_result = results[i-1]
                throughput_change = ((result['throughput'] - prev_result['throughput']) / 
                                   prev_result['throughput'] * 100)
                logger.info(f"  {prev_result['count']}→{result['count']}文本: "
                          f"吞吐量变化 {throughput_change:+.1f}%")
        
        return True
        
    except Exception as e:
        logger.error(f"批量编码性能测试失败: {e}")
        return False


def test_adaptive_batch_sizing():
    """测试自适应批处理大小"""
    logger.info("=== 测试自适应批处理大小 ===")
    
    try:
        rag_system = RAGSystem()
        if not rag_system.initialize():
            logger.error("RAG系统初始化失败")
            return False
        
        # 测试不同文本数量下的批处理大小选择
        test_counts = [5, 20, 50, 100, 200]
        
        for count in test_counts:
            optimal_size = rag_system._get_optimal_batch_size(count)
            memory_percent = psutil.virtual_memory().percent
            
            logger.info(f"文本数量: {count}, 最优批处理大小: {optimal_size}, "
                       f"当前内存使用: {memory_percent:.1f}%")
        
        # 测试内存压力下的批处理调整
        logger.info("模拟不同内存使用情况:")
        original_threshold = rag_system.config.MEMORY_THRESHOLD
        
        # 模拟高内存使用
        rag_system.config.MEMORY_THRESHOLD = 50.0  # 降低阈值模拟高内存使用
        high_mem_size = rag_system._get_optimal_batch_size(100)
        
        # 恢复原设置
        rag_system.config.MEMORY_THRESHOLD = original_threshold
        normal_size = rag_system._get_optimal_batch_size(100)
        
        logger.info(f"正常情况批处理大小: {normal_size}")
        logger.info(f"高内存使用批处理大小: {high_mem_size}")
        
        return True
        
    except Exception as e:
        logger.error(f"自适应批处理测试失败: {e}")
        return False


def test_text_preprocessing_efficiency():
    """测试文本预处理效率"""
    logger.info("=== 测试文本预处理效率 ===")
    
    try:
        rag_system = RAGSystem()
        if not rag_system.initialize():
            logger.error("RAG系统初始化失败")
            return False
        
        # 测试不同长度的文本预处理
        test_texts = [
            "短文本",
            "中等长度的文本，包含一些金融术语和专业词汇，用于测试预处理效果。",
            "这是一个很长的文本，" * 50 + "用于测试文本截断功能的效果。",
            "   包含多余空格   的   文本   ",
            "\n\n包含换行符\n和制表符\t的文本\n\n"
        ]
        
        logger.info("测试文本预处理性能:")
        
        total_time = 0
        for i, text in enumerate(test_texts):
            start_time = time.time()
            processed = rag_system._preprocess_text(text)
            process_time = time.time() - start_time
            total_time += process_time
            
            logger.info(f"文本 {i+1}:")
            logger.info(f"  原长度: {len(text)}, 处理后长度: {len(processed)}")
            logger.info(f"  处理耗时: {process_time*1000:.3f}毫秒")
            logger.info(f"  原文本: {repr(text[:50])}...")
            logger.info(f"  处理后: {repr(processed[:50])}...")
            logger.info("")
        
        avg_time = total_time / len(test_texts)
        logger.info(f"平均预处理耗时: {avg_time*1000:.3f}毫秒")
        
        return True
        
    except Exception as e:
        logger.error(f"文本预处理效率测试失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始CPU性能优化测试")
    
    # 配置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:HH:mm:ss} | {level} | {message}")
    
    # 显示系统信息
    logger.info(f"系统信息:")
    logger.info(f"  CPU核数: {psutil.cpu_count()}")
    logger.info(f"  内存总量: {psutil.virtual_memory().total / 1024**3:.1f}GB")
    logger.info(f"  当前CPU使用率: {psutil.cpu_percent()}%")
    logger.info(f"  当前内存使用率: {psutil.virtual_memory().percent}%")
    logger.info("")
    
    # 运行测试
    tests = [
        ("文本预处理效率测试", test_text_preprocessing_efficiency),
        ("单个编码性能测试", test_single_encoding_performance),
        ("自适应批处理测试", test_adaptive_batch_sizing),
        ("批量编码性能测试", test_batch_encoding_performance),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"开始 {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            start_time = time.time()
            result = test_func()
            test_time = time.time() - start_time
            
            results.append((test_name, result, test_time))
            
            if result:
                logger.info(f"✓ {test_name} 通过 (耗时: {test_time:.2f}秒)")
            else:
                logger.error(f"✗ {test_name} 失败")
                
        except Exception as e:
            logger.error(f"✗ {test_name} 异常: {e}")
            results.append((test_name, False, 0))
    
    # 输出总结
    logger.info(f"\n{'='*60}")
    logger.info("测试总结")
    logger.info(f"{'='*60}")
    
    passed = sum(1 for _, result, _ in results if result)
    total = len(results)
    
    for test_name, result, test_time in results:
        status = "✓ 通过" if result else "✗ 失败"
        logger.info(f"{test_name}: {status} ({test_time:.2f}秒)")
    
    logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！CPU优化功能正常工作。")
        return 0
    else:
        logger.error("❌ 部分测试失败，请检查配置和环境。")
        return 1


if __name__ == "__main__":
    exit(main())
